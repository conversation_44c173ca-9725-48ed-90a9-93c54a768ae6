"use client";

import React, { useState, useEffect } from "react";
import * as styles from "./SubcontractorApplicationForm.css";
import PersonalInfoStep from "./Steps/PersonalInfoStep";
import WorkPreferencesStep from "./Steps/WorkPreferencesStep";
import DocumentUploadStep from "./Steps/DocumentUploadStep";
import ConfirmationStep from "./Steps/ConfirmationStep";
import FormSidebar from "./FormSidebar/FormSidebar";
import { useRouter } from "next/navigation";
import { createClient } from "@supabase/supabase-js";

const STORAGE_KEY = "subcontractor_application_form_data";

export type FormData = {
  // Personal Information
  firstName: string;
  lastName: string;
  mobile: string;
  email: string;
  addressLine1: string;
  addressLine2: string;
  postCode: string;
  dateOfBirth: string;
  hearAboutUs: string;
  contactConsent: boolean;
  referred?: string;
  referrerName?: string;

  // Work Preferences
  gasRegistered: boolean | undefined;
  yearsExperience: number;
  travelDistance: number;
  hasOwnVan: boolean | undefined;
  hasOwnTools: boolean | undefined;
  workType: "Domestic" | "Commercial" | "Both" | undefined;
  centralLondon: boolean | undefined;
  drivingLicense: boolean | undefined;
  publicLiabilityInsurance: boolean | undefined;
  availableDays: string[];
  acceptedRates: boolean | undefined;
  outOfHoursWork: boolean | undefined;
  emergencyCallouts: boolean | undefined;
  preferredWorkType: string[];
  additionalQualifications: string;

  // Documents
  idPhoto: File | null;
  vanFrontPhoto: File | null;
  vanBackPhoto: File | null;
  vanLeftPhoto: File | null;
  vanRightPhoto: File | null;
  gasSafeCard: File | null;
  insuranceProof: File | null;
  additionalDocuments: File[];
};

const SubcontractorApplicationForm: React.FC = () => {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState<number>(1);
  const [formData, setFormData] = useState<FormData>(() => {
    // Try to load saved data from localStorage on initial render
    if (typeof window !== 'undefined') {
      const savedData = localStorage.getItem(STORAGE_KEY);
      if (savedData) {
        try {
          const parsedData = JSON.parse(savedData);
          // Convert File objects back to null since they can't be stored in localStorage
          return {
            ...parsedData,
            idPhoto: null,
            vanFrontPhoto: null,
            vanBackPhoto: null,
            vanLeftPhoto: null,
            vanRightPhoto: null,
            gasSafeCard: null,
            insuranceProof: null,
            additionalDocuments: [],
          };
        } catch (e) {
          console.error("Error parsing saved form data:", e);
        }
      }
    }

    return {
      firstName: "",
      lastName: "",
      mobile: "",
      email: "",
      addressLine1: "",
      addressLine2: "",
      postCode: "",
      dateOfBirth: "",
      hearAboutUs: "",
      contactConsent: false,

      gasRegistered: undefined,
      yearsExperience: 0,
      travelDistance: 0,
      hasOwnVan: undefined,
      hasOwnTools: undefined,
      workType: undefined,
      centralLondon: undefined,
      drivingLicense: undefined,
      publicLiabilityInsurance: undefined,
      availableDays: [],
      acceptedRates: undefined,
      outOfHoursWork: undefined,
      emergencyCallouts: undefined,
      preferredWorkType: [],
      additionalQualifications: "",

      // Documents
      idPhoto: null,
      vanFrontPhoto: null,
      vanBackPhoto: null,
      vanLeftPhoto: null,
      vanRightPhoto: null,
      gasSafeCard: null,
      insuranceProof: null,
      additionalDocuments: [],
    };
  });

  // Save form data to localStorage whenever it changes
  useEffect(() => {
    // Create a copy without File objects (they can't be serialized)
    const dataForStorage = {
      ...formData,
      idPhoto: null,
      vanFrontPhoto: null,
      vanBackPhoto: null,
      vanLeftPhoto: null,
      vanRightPhoto: null,
      gasSafeCard: null,
      insuranceProof: null,
      additionalDocuments: [],
    };
    
    localStorage.setItem(STORAGE_KEY, JSON.stringify(dataForStorage));
  }, [formData]);

  const handleNext = () => {
    setCurrentStep(prev => Math.min(prev + 1, 4));
  };

  const handleBack = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = async () => {
    try {
      const supabase = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_KEY!
      );

      const data = new FormData();

      Object.entries(formData).forEach(([key, value]) => {
        if (value === null || value === undefined) {
          return;
        }

        if (Array.isArray(value)) {
          value.forEach(item => {
            if (item instanceof File) {
              data.append(key, item);
            } else {
              data.append(key, item.toString());
            }
          });
        } else if (value instanceof File) {
          data.append(key, value);
        } else {
          data.append(key, value.toString());
        }
      });

      const { data: result, error } = await supabase.functions.invoke("create-subcontractor", {
        body: data,
      });
      if (result) {
        setCurrentStep(prev => prev + 1);
      }
      if (!error) {
        localStorage.removeItem(STORAGE_KEY);
        setCurrentStep(4);
      } else {
        alert(`Error submitting application: ${error.message}`);
        throw new Error(`Submission failed: ${error.message}`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error("Error submitting application:", errorMessage);
      alert(`An error occurred: ${errorMessage}`);
    }
  };

  const updateFormData = (data: Partial<FormData>) => {
    setFormData(prev => ({ ...prev, ...data }));
  };

  return (
    <div className={styles.formContainer}>
      <div className={styles.formWrapper}>
        <div className={styles.formContent}>
          <FormSidebar currentStep={currentStep} />

          <div className={styles.stepContainer}>
            {currentStep === 1 && (
              <PersonalInfoStep
                formData={formData}
                updateFormData={updateFormData}
                onNext={handleNext}
              />
            )}

            {currentStep === 2 && (
              <WorkPreferencesStep
                formData={formData}
                updateFormData={updateFormData}
                onNext={handleNext}
                onBack={handleBack}
              />
            )}

            {currentStep === 3 && (
              <DocumentUploadStep
                formData={formData}
                updateFormData={updateFormData}
                onSubmit={handleSubmit}
                onBack={handleBack}
              />
            )}

            {currentStep === 4 && (
              <ConfirmationStep
                onBackToHome={() => {}}
                onBack={handleBack}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubcontractorApplicationForm;
