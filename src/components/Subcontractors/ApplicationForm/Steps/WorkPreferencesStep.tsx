import React from "react";
import * as styles from "./Steps.css";
import { FormData } from "../SubcontractorApplicationForm";
import Button from "@/components/Button";
import Image from "next/image";
import arrowUp from "../../FAQ/arrow-up.svg";
import arrowDown from "../../FAQ/arrow-down.svg";
import { useForm } from "react-hook-form";
import { numberValidator } from "@/utils/validators";
import classNames from "classnames";
import {drivingLicense} from "./Steps.css";

interface WorkPreferencesStepProps {
  formData: FormData;
  updateFormData: (data: Partial<FormData>) => void;
  onNext: () => void;
  onBack: () => void;
}

const WorkPreferencesStep: React.FC<WorkPreferencesStepProps> = ({
  formData,
  updateFormData,
  onNext,
  onBack
}) => {
  const { 
    register, 
    handleSubmit, 
    setValue,
    watch,
    formState: { errors } 
  } = useForm({
    defaultValues: formData,
    mode: "onBlur"
  });

  const watchedAvailableDays = watch("availableDays") || [];
  const watchedPreferredWorkTypes = watch("preferredWorkType") || [];

  const onSubmit = (data: Partial<FormData>) => {
    updateFormData(data);
    onNext();
  };

  const handleDayToggle = (day: string) => {
    const currentDays = [...watchedAvailableDays];
    if (currentDays.includes(day)) {
      setValue("availableDays", currentDays.filter(d => d !== day), { shouldValidate: true });
    } else {
      setValue("availableDays", [...currentDays, day], { shouldValidate: true });
    }
  };

  const handleWorkTypeToggle = (workType: string) => {
    const currentWorkTypes = [...watchedPreferredWorkTypes];
    if (currentWorkTypes.includes(workType)) {
      setValue("preferredWorkType", currentWorkTypes.filter(wt => wt !== workType), { shouldValidate: true });
    } else {
      setValue("preferredWorkType", [...currentWorkTypes, workType], { shouldValidate: true });
    }
  };

  return (
    <div className={styles.stepWrapper}>
      <div className={styles.stepHeader}>
        <div className={styles.stepIndicator}>Step 2/4</div>
        <h2 className={styles.stepTitle}>Work Preferences & Qualifications</h2>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className={styles.form}>
        <div className={styles.formGroup}>
          <label className={styles.label}>
            Are you Gas Safe Registered? <span className={styles.required}>*</span>
          </label>
          <div className={styles.radioGroup}>
            <div className={styles.radioOption}>
              <input
                id="gasRegisteredYes"
                type="radio"
                value="true"
                {...register("gasRegistered", { required: "Please select an option" })}
                className={styles.radioInput}
              />
              <label htmlFor="gasRegisteredYes" className={styles.radioLabel}>Yes</label>
            </div>
            <div className={styles.radioOption}>
              <input
                id="gasRegisteredNo"
                type="radio"
                value="false"
                {...register("gasRegistered", { required: "Please select an option" })}
                className={styles.radioInput}
              />
              <label htmlFor="gasRegisteredNo" className={styles.radioLabel}>No</label>
            </div>
          </div>
          {errors.gasRegistered && <p className={styles.errorText}>{errors.gasRegistered.message}</p>}
        </div>

        <div className={styles.formRow}>
          <div className={styles.formGroup}>
            <label htmlFor="yearsExperience" className={styles.label}>
              How many years experience do you have?
            </label>
            <div className={styles.numberInputWrapper}>
              <input
                id="yearsExperience"
                type="text"
                className={styles.numberInput}
                {...register("yearsExperience", {
                  required: "Years of experience is required",
                  validate: (value) => numberValidator(value.toString()),
                })}
              />
              <div className={styles.numberControls}>
                <button 
                  type="button" 
                  className={styles.numberButton}
                  onClick={() => {
                    const currentValue = watch("yearsExperience") || 0;
                    setValue("yearsExperience", currentValue + 1, { shouldValidate: true });
                  }}
                >
                  <Image src={arrowUp.src} alt="arrow-up" width={12} height={12} />
                </button>
                <button 
                  type="button" 
                  className={styles.numberButton}
                  onClick={() => {
                    const currentValue = watch("yearsExperience") || 0;
                    setValue("yearsExperience", Math.max(0, currentValue - 1), { shouldValidate: true });
                  }}
                >
                  <Image src={arrowDown.src} alt="arrow-down" width={12} height={12} />
                </button>
              </div>
            </div>
            {errors.yearsExperience && <p className={styles.errorText}>{errors.yearsExperience.message}</p>}
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="travelDistance" className={styles.label}>
              How far are you willing to travel for a job?
            </label>
            <div className={styles.numberInputWrapper}>
              <input
                id="travelDistance"
                type="text"
                className={styles.numberInput}
                {...register("travelDistance", {
                  required: "Travel distance is required",
                  validate: (value) => numberValidator(value.toString()),
                })}
              />
              <div className={styles.numberControls}>
                <button 
                  type="button" 
                  className={styles.numberButton}
                  onClick={() => {
                    const currentValue = watch("travelDistance") || 0;
                    setValue("travelDistance", currentValue + 1, { shouldValidate: true });
                  }}
                >
                  <Image src={arrowUp.src} alt="arrow-up" width={12} height={12} />
                </button>
                <button 
                  type="button" 
                  className={styles.numberButton}
                  onClick={() => {
                    const currentValue = watch("travelDistance") || 0;
                    setValue("travelDistance", Math.max(0, currentValue - 1), { shouldValidate: true });
                  }}
                >
                  <Image src={arrowDown.src} alt="arrow-down" width={12} height={12} />
                </button>
              </div>
              <span className={styles.inputSuffix}>miles</span>
            </div>
            {errors.travelDistance && <p className={styles.errorText}>{errors.travelDistance.message}</p>}
          </div>
        </div>

        <div className={styles.formRow}>
          <div className={styles.formGroup}>
            <label className={styles.label}>
              Do you own your own van? <span className={styles.required}>*</span>
            </label>
            <div className={styles.radioGroup}>
              <div className={styles.radioOption}>
                <input
                  id="ownVanYes"
                  type="radio"
                  value="true"
                  {...register("hasOwnVan", { required: "Please select an option" })}
                  className={styles.radioInput}
                />
                <label htmlFor="ownVanYes" className={styles.radioLabel}>Yes</label>
              </div>
              <div className={styles.radioOption}>
                <input
                  id="ownVanNo"
                  type="radio"
                  value="false"
                  {...register("hasOwnVan", { required: "Please select an option" })}
                  className={styles.radioInput}
                />
                <label htmlFor="ownVanNo" className={styles.radioLabel}>No</label>
              </div>
            </div>
            {errors.hasOwnVan && <p className={styles.errorText}>{errors.hasOwnVan.message}</p>}
          </div>

          <div className={styles.formGroup}>
            <label className={styles.label}>
              Do you own your own tools? <span className={styles.required}>*</span>
            </label>
            <div className={styles.radioGroup}>
              <div className={styles.radioOption}>
                <input
                  id="ownToolsYes"
                  type="radio"
                  value="true"
                  {...register("hasOwnTools", { required: "Please select an option" })}
                  className={styles.radioInput}
                />
                <label htmlFor="ownToolsYes" className={styles.radioLabel}>Yes</label>
              </div>
              <div className={styles.radioOption}>
                <input
                  id="ownToolsNo"
                  type="radio"
                  value="false"
                  {...register("hasOwnTools", { required: "Please select an option" })}
                  className={styles.radioInput}
                />
                <label htmlFor="ownToolsNo" className={styles.radioLabel}>No</label>
              </div>
            </div>
            {errors.hasOwnTools && <p className={styles.errorText}>{errors.hasOwnTools.message}</p>}
          </div>
        </div>

        <div className={styles.formGroup}>
          <label className={styles.label}>
            Do you work in Domestic, Commercial or Both? <span className={styles.required}>*</span>
          </label>
          <div className={styles.radioGroup}>
            <div className={styles.radioOption}>
              <input
                id="workTypeDomestic"
                type="radio"
                value="Domestic"
                {...register("workType", { required: "Please select an option" })}
                className={styles.radioInput}
              />
              <label htmlFor="workTypeDomestic" className={styles.radioLabel}>Domestic</label>
            </div>
            <div className={styles.radioOption}>
              <input
                id="workTypeCommercial"
                type="radio"
                value="Commercial"
                {...register("workType", { required: "Please select an option" })}
                className={styles.radioInput}
              />
              <label htmlFor="workTypeCommercial" className={styles.radioLabel}>Commercial</label>
            </div>
            <div className={styles.radioOption}>
              <input
                id="workTypeBoth"
                type="radio"
                value="Both"
                {...register("workType", { required: "Please select an option" })}
                className={styles.radioInput}
              />
              <label htmlFor="workTypeBoth" className={styles.radioLabel}>Both</label>
            </div>
          </div>
          {errors.workType && <p className={styles.errorText}>{errors.workType.message}</p>}
        </div>

        <div className={classNames(styles.formRow)}>
          <div className={styles.formGroup}>
            <label className={styles.label}>
              Are you happy to travel into Central London? <span className={styles.required}>*</span>
            </label>
            <div className={styles.radioGroup}>
              <div className={styles.radioOption}>
                <input
                  id="centralLondonYes"
                  type="radio"
                  value="true"
                  {...register("centralLondon", { required: "Please select an option" })}
                  className={styles.radioInput}
                />
                <label htmlFor="centralLondonYes" className={styles.radioLabel}>Yes</label>
              </div>
              <div className={styles.radioOption}>
                <input
                  id="centralLondonNo"
                  type="radio"
                  value="false"
                  {...register("centralLondon", { required: "Please select an option" })}
                  className={styles.radioInput}
                />
                <label htmlFor="centralLondonNo" className={styles.radioLabel}>No</label>
              </div>
            </div>
            {errors.centralLondon && <p className={styles.errorText}>{errors.centralLondon.message}</p>}
          </div>

          <div className={styles.formGroup}>
            <label className={classNames(styles.drivingLicense, styles.label)}>
              Do you have a full UK Driving License? <span className={styles.required}>*</span>
            </label>
            <div className={styles.radioGroup}>
              <div className={styles.radioOption}>
                <input
                  id="drivingLicenseYes"
                  type="radio"
                  value="true"
                  {...register("drivingLicense", { required: "Please select an option" })}
                  className={styles.radioInput}
                />
                <label htmlFor="drivingLicenseYes" className={styles.radioLabel}>Yes</label>
              </div>
              <div className={styles.radioOption}>
                <input
                  id="drivingLicenseNo"
                  type="radio"
                  value="false"
                  {...register("drivingLicense", { required: "Please select an option" })}
                  className={styles.radioInput}
                />
                <label htmlFor="drivingLicenseNo" className={styles.radioLabel}>No</label>
              </div>
            </div>
            {errors.drivingLicense && <p className={styles.errorText}>{errors.drivingLicense.message}</p>}
          </div>
        </div>

        <div className={styles.formGroup}>
          <label className={styles.label}>
            Do you hold valid Public Liability Insurance? <span className={styles.required}>*</span>
          </label>
          <div className={styles.radioGroup}>
            <div className={styles.radioOption}>
              <input
                id="insuranceYes"
                type="radio"
                value="true"
                {...register("publicLiabilityInsurance", { required: "Please select an option" })}
                className={styles.radioInput}
              />
              <label htmlFor="insuranceYes" className={styles.radioLabel}>Yes</label>
            </div>
            <div className={styles.radioOption}>
              <input
                id="insuranceNo"
                type="radio"
                value="false"
                {...register("publicLiabilityInsurance", { required: "Please select an option" })}
                className={styles.radioInput}
              />
              <label htmlFor="insuranceNo" className={styles.radioLabel}>No</label>
            </div>
          </div>
          {errors.publicLiabilityInsurance && <p className={styles.errorText}>{errors.publicLiabilityInsurance.message}</p>}
        </div>

        <div className={styles.formGroup}>
          <label className={styles.label}>
            What days are you available to work? <span className={styles.required}>*</span>
          </label>
          <div className={styles.checkboxGroup}>
            {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map(day => (
              <div key={day} className={styles.checkboxOption}>
                <input
                  id={`day-${day}`}
                  type="checkbox"
                  checked={watchedAvailableDays.includes(day)}
                  onChange={() => handleDayToggle(day)}
                  className={styles.checkbox}
                />
                <label htmlFor={`day-${day}`} className={styles.checkboxLabel}>{day}</label>
              </div>
            ))}
          </div>
          <input 
            type="hidden" 
            {...register("availableDays", { 
              validate: value => (value && value.length > 0) || "Please select at least one day" 
            })} 
          />
          {errors.availableDays && <p className={styles.errorText}>{errors.availableDays.message}</p>}
        </div>

        <div className={styles.formGroup}>
          <label className={styles.label}>
            Are you happy to work for £70 per hour for Domestic and £80 per hour for Commercial work? <span className={styles.required}>*</span>
          </label>
          <div className={styles.radioGroup}>
            <div className={styles.radioOption}>
              <input
                id="acceptedRatesYes"
                type="radio"
                value="true"
                {...register("acceptedRates", { required: "Please select an option" })}
                className={styles.radioInput}
              />
              <label htmlFor="acceptedRatesYes" className={styles.radioLabel}>Yes</label>
            </div>
            <div className={styles.radioOption}>
              <input
                id="acceptedRatesNo"
                type="radio"
                value="false"
                {...register("acceptedRates", { required: "Please select an option" })}
                className={styles.radioInput}
              />
              <label htmlFor="acceptedRatesNo" className={styles.radioLabel}>No</label>
            </div>
          </div>
          {errors.acceptedRates && <p className={styles.errorText}>{errors.acceptedRates.message}</p>}
        </div>

        <div className={styles.formRow}>
          <div className={styles.formGroup}>
            <label className={styles.label}>
              Are you happy to work out-of-hours? <span className={styles.required}>*</span>
            </label>
            <div className={styles.radioGroup}>
              <div className={styles.radioOption}>
                <input
                  id="outOfHoursYes"
                  type="radio"
                  value="true"
                  {...register("outOfHoursWork", { required: "Please select an option" })}
                  className={styles.radioInput}
                />
                <label htmlFor="outOfHoursYes" className={styles.radioLabel}>Yes</label>
              </div>
              <div className={styles.radioOption}>
                <input
                  id="outOfHoursNo"
                  type="radio"
                  value="false"
                  {...register("outOfHoursWork", { required: "Please select an option" })}
                  className={styles.radioInput}
                />
                <label htmlFor="outOfHoursNo" className={styles.radioLabel}>No</label>
              </div>
            </div>
            {errors.outOfHoursWork && <p className={styles.errorText}>{errors.outOfHoursWork.message}</p>}
          </div>

          <div className={styles.formGroup}>
            <label className={styles.label}>
              Are you open to emergency callouts? <span className={styles.required}>*</span>
            </label>
            <div className={styles.radioGroup}>
              <div className={styles.radioOption}>
                <input
                  id="emergencyCalloutsYes"
                  type="radio"
                  value="true"
                  {...register("emergencyCallouts", { required: "Please select an option" })}
                  className={styles.radioInput}
                />
                <label htmlFor="emergencyCalloutsYes" className={styles.radioLabel}>Yes</label>
              </div>
              <div className={styles.radioOption}>
                <input
                  id="emergencyCalloutsNo"
                  type="radio"
                  value="false"
                  {...register("emergencyCallouts", { required: "Please select an option" })}
                  className={styles.radioInput}
                />
                <label htmlFor="emergencyCalloutsNo" className={styles.radioLabel}>No</label>
              </div>
            </div>
            {errors.emergencyCallouts && <p className={styles.errorText}>{errors.emergencyCallouts.message}</p>}
          </div>
        </div>

        <div className={styles.formGroup}>
          <label className={styles.label}>
            What is your preferred type of work? <span className={styles.required}>*</span>
          </label>
          <div className={styles.checkboxGroup}>
            {['Reactive', 'Maintenance', 'Installations'].map(workType => (
              <div key={workType} className={styles.checkboxOption}>
                <input
                  id={`workType-${workType}`}
                  type="checkbox"
                  checked={watchedPreferredWorkTypes.includes(workType)}
                  onChange={() => handleWorkTypeToggle(workType)}
                  className={styles.checkbox}
                />
                <label htmlFor={`workType-${workType}`} className={styles.checkboxLabel}>{workType}</label>
              </div>
            ))}
          </div>
          <input 
            type="hidden" 
            {...register("preferredWorkType", { 
              validate: value => (value && value.length > 0) || "Please select at least one work type" 
            })} 
          />
          {errors.preferredWorkType && <p className={styles.errorText}>{errors.preferredWorkType.message}</p>}
        </div>

        <div className={styles.formGroup}>
          <label htmlFor="additionalQualifications" className={styles.label}>Any additional qualifications or relevant certifications?</label>
          <input
            id="additionalQualifications"
            className={styles.textarea}
            placeholder="Enter your text"
            value={formData.additionalQualifications}
            onChange={(e) => updateFormData({ additionalQualifications: e.target.value })}
          />
        </div>

        <div className={styles.buttonGroup}>
          <Button
            type="button"
            variant="outlined"
            color="secondary"
            onClick={onBack}
            className={styles.backButton}
          >
            ← Back
          </Button>
          <Button
            type="submit"
            className={styles.nextButton}
          >
            Upload Documents →
          </Button>
        </div>
      </form>
    </div>
  );
};

export default WorkPreferencesStep;
